#ifndef __TOUCHKEY_H__
#define __TOUCHKEY_H__

#include "sys.h"


extern u32 step_f;
extern u32 start_f;
extern u32 end_f;
extern u32 out_f;
extern u8 on_off;
extern u8 mod;
extern u8 again;


extern void py_show_result(void);
//extern void Draw_cursor_right(void);
//extern void Draw_cursor_left(void);
//extern void start_f_up(void);
//extern void end_f_up(void);
//extern void step_f_up(void);
//extern void start_f_down(void);
//extern void end_f_down(void);
//extern void step_f_down(void);





#endif
